"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ooter, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  Play,
  Pause,
  Download,
  Heart,
  Share2,
  MoreHorizontal,
  Clock,
  Verified
} from "lucide-react";
import { cn } from "@/lib/utils";
import { SimpleWaveform } from "../waveform/waveform-display";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Track } from "@/types/music";
import { generateTrackUrl } from "@/lib/track-slug";
import Link from "next/link";
import { useLocale } from "next-intl";
import { useAudioPlayer } from "@/contexts/audio-player-context";

interface TrackCardProps {
  track: Track;
  isPlaying?: boolean;
  currentTime?: number;
  showUser?: boolean;
  showWaveform?: boolean;
  compact?: boolean;
  className?: string;
  onPlay?: (track: Track) => void;
  onPause?: () => void;
  onDownload?: (track: Track) => void;
  onLike?: (track: Track) => void;
  onShare?: (track: Track) => void;
  onAddToCollection?: (track: Track) => void;
}

export default function TrackCard({
  track,
  isPlaying = false,
  currentTime = 0,
  showUser = true,
  showWaveform = true,
  compact = false,
  className,
  onPlay,
  onPause,
  onDownload,
  onLike,
  onShare,
  onAddToCollection,
}: TrackCardProps) {
  const [isLiked, setIsLiked] = useState(false);
  const locale = useLocale();
  const { currentTrack, isPlaying: globalIsPlaying, play, pause } = useAudioPlayer();

  // Check if this track is currently playing
  const isCurrentTrack = currentTrack?.uuid === track.uuid;
  const isTrackPlaying = isCurrentTrack && globalIsPlaying;

  const handlePlayPause = async () => {
    if (isTrackPlaying) {
      pause();
      onPause?.();
    } else {
      await play(track);
      onPlay?.(track);
    }
  };

  const handleLike = () => {
    setIsLiked(!isLiked);
    onLike?.(track);
  };

  const handleDownload = () => {
    onDownload?.(track);
  };

  const handleShare = () => {
    onShare?.(track);
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return "";
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(1)} MB`;
  };



  // Generate track URL for navigation
  const trackUrl = generateTrackUrl(track, locale);

  return (
    <Card className={cn(
      "bg-card rounded-xl shadow border border-border/20 flex flex-col transition-all duration-200 h-32",
      className
    )}>
      <div className="flex flex-col h-full px-5 py-3">
        {/* 标题 */}
        <Link href={trackUrl}>
          <h3 className="text-base font-bold leading-tight line-clamp-2 break-words mb-2">
            {track.title || "Untitled Track"}
          </h3>
        </Link>
        {/* 主信息区：左右分布 */}
        <div className="flex items-center justify-between h-9">
          {/* 左侧：播放、时长、点赞 */}
          <div className="flex items-center gap-1.5">
            <Button
              variant="default"
              size="icon"
              className="h-8 w-8 rounded-full p-0 shadow-sm"
              onClick={handlePlayPause}
            >
              {isTrackPlaying ? (
                <Pause className="h-4 w-4" />
              ) : (
                <Play className="h-4 w-4 ml-0.5" />
              )}
            </Button>
            <span className="text-xs text-muted-foreground flex items-center gap-1">
              <Clock className="h-4 w-4" />
              {formatDuration(track.duration)}
            </span>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleLike}
              className={cn(
                "h-8 w-8 p-0 hover:bg-red-50 hover:text-red-500 transition-colors",
                isLiked && "text-red-500 hover:text-red-600"
              )}
            >
              <Heart className={cn("h-4 w-4", isLiked && "fill-current")} />
            </Button>
          </div>
          {/* 右侧：状态、文件大小 */}
          <div className="flex items-center gap-1.5">
            <div className={cn(
              "px-2 py-0.5 rounded-full text-xs font-semibold",
              track.is_public
                ? "bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400"
                : "bg-orange-100 text-orange-700 dark:bg-orange-900/20 dark:text-orange-400"
            )}>
              {track.is_public ? "Public" : "Private"}
            </div>
            {track.file_size && (
              <span className="font-medium text-xs">{formatFileSize(track.file_size)}</span>
            )}
          </div>
        </div>
        {/* 波形区可选 */}
        {showWaveform && track.waveform_data && (
          <div className="mt-2">
            <SimpleWaveform
              peaks={track.waveform_data.peaks}
              currentTime={currentTime}
              duration={track.duration}
              height={14}
              interactive={true}
              onSeek={(time) => {
                console.log("Seek to:", time);
              }}
            />
          </div>
        )}
      </div>
    </Card>
  );
}
