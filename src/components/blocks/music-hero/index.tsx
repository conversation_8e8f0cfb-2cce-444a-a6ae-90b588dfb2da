"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Music, Play, Sparkles, Zap, Clock, Download } from "lucide-react";
import { <PERSON> } from "@/i18n/navigation";
import TrackCard from "@/components/music/track/track-card";
import { Track } from "@/types/music";
import { useState, useEffect } from "react";

// API response interface
interface FeaturedTracksResponse {
  tracks: Track[];
  total: number;
  source: "database" | "fallback";
}

export default function MusicHero() {
  const [tracks, setTracks] = useState<Track[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch featured tracks from API
  useEffect(() => {
    const fetchFeaturedTracks = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch("/api/tracks/featured?limit=4");

        if (!response.ok) {
          throw new Error("Failed to fetch featured tracks");
        }

        const result = await response.json();

        if (result.code !== 0) {
          throw new Error(result.message || "Failed to fetch featured tracks");
        }

        const data: FeaturedTracksResponse = result.data;
        setTracks(data.tracks);
      } catch (err) {
        console.error("Failed to load featured tracks:", err);
        setError(err instanceof Error ? err.message : "Failed to load featured tracks");
        setTracks([]);
      } finally {
        setIsLoading(false);
      }
    };

    // Add a small delay to ensure client-side execution
    const timer = setTimeout(() => {
      fetchFeaturedTracks();
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  const handleTrackPlay = (track: Track) => {
    // Global audio player will handle this through TrackCard
  };

  return (
    <section className="relative py-24 overflow-hidden">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5" />
      
      <div className="container relative">
        <div className="text-center mb-16">
          {/* Announcement badge */}
          <div className="flex items-center justify-center mb-8">
            <Badge variant="secondary" className="px-4 py-2 text-sm">
              <Sparkles className="mr-2 h-4 w-4" />
              AI-Powered Music Generation
            </Badge>
          </div>

          {/* Main heading */}
          <h1 className="mx-auto mb-6 max-w-4xl text-balance text-4xl font-bold lg:text-6xl">
            Create Perfect{" "}
            <span className="bg-gradient-to-r from-primary via-primary to-secondary bg-clip-text text-transparent">
              Loop Music
            </span>{" "}
            in Seconds
          </h1>

          {/* Description */}
          <p className="mx-auto max-w-2xl text-lg text-muted-foreground mb-8">
            Generate seamless, high-quality music loops for your videos, podcasts, 
            games, and creative projects. Powered by advanced AI technology.
          </p>

          {/* CTA buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Link href="/generate">
              <Button
                size="lg"
                className="px-8"
              >
                <Music className="mr-2 h-5 w-5" />
                Start Creating Music
              </Button>
            </Link>
            <Link href="/explore">
              <Button variant="outline" size="lg" className="px-8">
                <Play className="mr-2 h-5 w-5" />
                Explore Examples
              </Button>
            </Link>
          </div>

          {/* Key features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto">
            <div className="flex items-center gap-3 text-sm text-muted-foreground">
              <div className="p-2 bg-primary/10 rounded-full">
                <Zap className="h-4 w-4 text-primary" />
              </div>
              <span>Generate in 30 seconds</span>
            </div>
            <div className="flex items-center gap-3 text-sm text-muted-foreground">
              <div className="p-2 bg-primary/10 rounded-full">
                <Clock className="h-4 w-4 text-primary" />
              </div>
              <span>Perfect seamless loops</span>
            </div>
            <div className="flex items-center gap-3 text-sm text-muted-foreground">
              <div className="p-2 bg-primary/10 rounded-full">
                <Download className="h-4 w-4 text-primary" />
              </div>
              <span>Commercial use license</span>
            </div>
          </div>
        </div>



        {/* Featured tracks */}
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold mb-2">Featured Music Loops</h2>
            <p className="text-muted-foreground">
              Discover what's possible with LoopCraft AI
            </p>
          </div>

          {/* Loading State */}
          {isLoading && (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading featured tracks...</p>
            </div>
          )}

          {/* Error State */}
          {error && !isLoading && (
            <div className="text-center py-12">
              <div className="text-red-500 mb-4">
                <Music className="h-12 w-12 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Failed to Load Featured Tracks</h3>
                <p className="text-muted-foreground mb-4">{error}</p>
                <Button
                  variant="outline"
                  onClick={() => window.location.reload()}
                >
                  Try Again
                </Button>
              </div>
            </div>
          )}

          {/* Tracks Grid */}
          {!isLoading && !error && tracks.length > 0 && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {tracks.map((track) => (
                  <TrackCard
                    key={track.uuid}
                    track={track}
                    showUser={false}
                    onPlay={handleTrackPlay}
                  />
                ))}
              </div>

              <div className="text-center mt-8">
                <Link href="/explore">
                  <Button variant="outline">
                    View All Examples
                  </Button>
                </Link>
              </div>
            </>
          )}

          {/* Empty State */}
          {!isLoading && !error && tracks.length === 0 && (
            <div className="text-center py-12">
              <Music className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Featured Tracks Available</h3>
              <p className="text-muted-foreground mb-4">
                Check back later for amazing AI-generated music examples
              </p>
              <Link href="/explore">
                <Button variant="outline">
                  Explore All Tracks
                </Button>
              </Link>
            </div>
          )}
        </div>

        {/* Stats section */}
        <div className="mt-24 grid grid-cols-2 md:grid-cols-4 gap-8 max-w-2xl mx-auto">
          <div className="text-center">
            <div className="text-3xl font-bold text-primary mb-1">50K+</div>
            <div className="text-sm text-muted-foreground">Tracks Generated</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary mb-1">10K+</div>
            <div className="text-sm text-muted-foreground">Happy Creators</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary mb-1">95%</div>
            <div className="text-sm text-muted-foreground">Loop Quality</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary mb-1">24/7</div>
            <div className="text-sm text-muted-foreground">AI Available</div>
          </div>
        </div>

        {/* Trust indicators */}
        <div className="mt-16 text-center">
          <p className="text-sm text-muted-foreground mb-6">
            Trusted by content creators worldwide
          </p>
          <div className="flex justify-center items-center gap-8 opacity-60">
            {/* Placeholder for brand logos */}
            <div className="h-8 w-20 bg-muted rounded" />
            <div className="h-8 w-20 bg-muted rounded" />
            <div className="h-8 w-20 bg-muted rounded" />
            <div className="h-8 w-20 bg-muted rounded" />
          </div>
        </div>
      </div>
    </section>
  );
}
