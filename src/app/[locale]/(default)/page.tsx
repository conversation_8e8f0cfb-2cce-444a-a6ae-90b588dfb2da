import MusicHero from "@/components/blocks/music-hero";
import MusicFeatures from "@/components/blocks/music-features";
import CTA from "@/components/blocks/cta";
import FAQ from "@/components/blocks/faq";
import Pricing from "@/components/blocks/pricing";
import Testimonial from "@/components/blocks/testimonial";
import { getLandingPage } from "@/services/page";
import { Metadata } from "next";
import { getTranslations } from "next-intl/server";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations("metadata");

  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}`;
  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}`;
  }

  const title = t("title");
  const description = t("description");
  const keywords = t("keywords");

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: "website",
      url: canonicalUrl,
      siteName: "LoopCraft",
      locale: locale,
      images: [
        {
          url: `${process.env.NEXT_PUBLIC_WEB_URL}/api/og/home`,
          width: 1200,
          height: 630,
          alt: "LoopCraft - AI-Powered Seamless Music Loop Generator",
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: [`${process.env.NEXT_PUBLIC_WEB_URL}/api/og/home`],
    },
    alternates: {
      canonical: canonicalUrl,
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

export default async function LandingPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getLandingPage(locale);

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    name: "LoopCraft",
    description: "AI-powered seamless music loop generator for content creators",
    url: `${process.env.NEXT_PUBLIC_WEB_URL}${locale !== "en" ? `/${locale}` : ""}`,
    applicationCategory: "MultimediaApplication",
    operatingSystem: "Web Browser",
    offers: {
      "@type": "Offer",
      price: "0",
      priceCurrency: "USD",
      description: "Free tier with credit-based usage",
    },
    creator: {
      "@type": "Organization",
      name: "LoopCraft",
      url: process.env.NEXT_PUBLIC_WEB_URL,
    },
    featureList: [
      "AI-powered music generation",
      "Seamless loop creation",
      "Commercial use licensing",
      "Multiple music styles",
      "Professional audio quality",
      "Fast generation (30 seconds)",
    ],
    screenshot: `${process.env.NEXT_PUBLIC_WEB_URL}/api/og/home`,
  };

  return (
    <>
      {/* Schema.org structured data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />

      <MusicHero />
      <MusicFeatures />
      {page.pricing && <Pricing pricing={page.pricing} />}
      {page.testimonial && <Testimonial section={page.testimonial} />}
      {page.faq && <FAQ section={page.faq} />}
      {page.cta && <CTA section={page.cta} />}
    </>
  );
}
