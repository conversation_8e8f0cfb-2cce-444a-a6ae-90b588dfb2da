import { NextResponse } from "next/server";
import { getPostsByLocale } from "@/models/post";

export async function GET() {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || "https://loopcraft.app";
    const currentDate = new Date().toISOString();

    // Static pages
    const staticPages = [
      {
        url: baseUrl,
        lastmod: currentDate,
        changefreq: "daily",
        priority: "1.0",
      },
      {
        url: `${baseUrl}/generate`,
        lastmod: currentDate,
        changefreq: "daily",
        priority: "0.9",
      },
      {
        url: `${baseUrl}/explore`,
        lastmod: currentDate,
        changefreq: "daily",
        priority: "0.8",
      },
      {
        url: `${baseUrl}/posts`,
        lastmod: currentDate,
        changefreq: "weekly",
        priority: "0.7",
      },
      {
        url: `${baseUrl}/pricing`,
        lastmod: currentDate,
        changefreq: "monthly",
        priority: "0.6",
      },
    ];

    // Get blog posts
    const posts = await getPostsByLocale("en", 1, 100) || [];
    const blogPages = posts.map((post) => ({
      url: `${baseUrl}/posts/${post.slug}`,
      lastmod: post.updated_at?.toISOString() || currentDate,
      changefreq: "monthly",
      priority: "0.6",
    }));

    // Combine all pages
    const allPages = [...staticPages, ...blogPages];

    // Generate XML sitemap
    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${allPages
  .map(
    (page) => `  <url>
    <loc>${page.url}</loc>
    <lastmod>${page.lastmod}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
  </url>`
  )
  .join("\n")}
</urlset>`;

    return new NextResponse(sitemap, {
      headers: {
        "Content-Type": "application/xml",
        "Cache-Control": "public, max-age=3600, s-maxage=3600",
      },
    });
  } catch (error) {
    console.error("Error generating sitemap:", error);
    return new NextResponse("Error generating sitemap", { status: 500 });
  }
}
