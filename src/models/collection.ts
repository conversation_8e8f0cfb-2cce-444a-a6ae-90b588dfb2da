import { userTrackCollections, collectionTracks, tracks } from "@/db/schema";
import { db } from "@/db";
import { desc, eq, and, sql } from "drizzle-orm";
import { UserTrackCollection } from "@/types/music";

// Insert new collection
export async function insertUserTrackCollection(
  data: typeof userTrackCollections.$inferInsert
): Promise<typeof userTrackCollections.$inferSelect | undefined> {
  const [collection] = await db()
    .insert(userTrackCollections)
    .values(data)
    .returning();

  return collection;
}

// Find collection by UUID
export async function findCollectionByUuid(
  uuid: string
): Promise<typeof userTrackCollections.$inferSelect | undefined> {
  const [collection] = await db()
    .select()
    .from(userTrackCollections)
    .where(eq(userTrackCollections.uuid, uuid))
    .limit(1);

  return collection;
}

// Get user's collections
export async function getUserCollections(
  user_uuid: string
): Promise<typeof userTrackCollections.$inferSelect[]> {
  const collections = await db()
    .select()
    .from(userTrackCollections)
    .where(eq(userTrackCollections.user_uuid, user_uuid))
    .orderBy(desc(userTrackCollections.created_at));

  return collections;
}

// Get public collections
export async function getPublicCollections(
  limit: number = 20,
  offset: number = 0
): Promise<typeof userTrackCollections.$inferSelect[]> {
  const collections = await db()
    .select()
    .from(userTrackCollections)
    .where(eq(userTrackCollections.is_public, true))
    .orderBy(desc(userTrackCollections.created_at))
    .limit(limit)
    .offset(offset);

  return collections;
}

// Update collection
export async function updateUserTrackCollection(
  uuid: string,
  data: Partial<typeof userTrackCollections.$inferInsert>
): Promise<typeof userTrackCollections.$inferSelect | undefined> {
  const [collection] = await db()
    .update(userTrackCollections)
    .set({
      ...data,
      updated_at: new Date(),
    })
    .where(eq(userTrackCollections.uuid, uuid))
    .returning();

  return collection;
}

// Delete collection
export async function deleteUserTrackCollection(uuid: string): Promise<boolean> {
  // First delete all collection tracks
  await db()
    .delete(collectionTracks)
    .where(eq(collectionTracks.collection_uuid, uuid));

  // Then delete the collection
  const result = await db()
    .delete(userTrackCollections)
    .where(eq(userTrackCollections.uuid, uuid));

  return result.length > 0;
}

// Add track to collection
export async function addTrackToCollection(
  collection_uuid: string,
  track_uuid: string
): Promise<typeof collectionTracks.$inferSelect | undefined> {
  // Check if track is already in collection
  const [existing] = await db()
    .select()
    .from(collectionTracks)
    .where(
      and(
        eq(collectionTracks.collection_uuid, collection_uuid),
        eq(collectionTracks.track_uuid, track_uuid)
      )
    )
    .limit(1);

  if (existing) {
    return existing;
  }

  const [collectionTrack] = await db()
    .insert(collectionTracks)
    .values({
      collection_uuid,
      track_uuid,
      added_at: new Date(),
    })
    .returning();

  return collectionTrack;
}

// Remove track from collection
export async function removeTrackFromCollection(
  collection_uuid: string,
  track_uuid: string
): Promise<boolean> {
  const result = await db()
    .delete(collectionTracks)
    .where(
      and(
        eq(collectionTracks.collection_uuid, collection_uuid),
        eq(collectionTracks.track_uuid, track_uuid)
      )
    );

  return result.length > 0;
}

// Get collection tracks
export async function getCollectionTracks(
  collection_uuid: string
): Promise<typeof tracks.$inferSelect[]> {
  const result = await db()
    .select({
      id: tracks.id,
      uuid: tracks.uuid,
      generation_uuid: tracks.generation_uuid,
      user_uuid: tracks.user_uuid,
      title: tracks.title,
      slug: tracks.slug,
      prompt: tracks.prompt,
      style: tracks.style,
      mood: tracks.mood,
      bpm: tracks.bpm,
      duration: tracks.duration,
      // New fields for API compatibility
      genre: tracks.genre,
      instrument: tracks.instrument,
      theme: tracks.theme,
      key_signature: tracks.key_signature,
      file_url: tracks.file_url,
      file_size: tracks.file_size,
      file_format: tracks.file_format,
      waveform_data: tracks.waveform_data,
      metadata: tracks.metadata,
      download_count: tracks.download_count,
      is_public: tracks.is_public,
      is_premium: tracks.is_premium,
      has_watermark: tracks.has_watermark,
      original_file_url: tracks.original_file_url,
      created_at: tracks.created_at,
      updated_at: tracks.updated_at,
    })
    .from(collectionTracks)
    .innerJoin(tracks, eq(collectionTracks.track_uuid, tracks.uuid))
    .where(eq(collectionTracks.collection_uuid, collection_uuid))
    .orderBy(desc(collectionTracks.added_at));

  return result;
}

// Check if track is in collection
export async function isTrackInCollection(
  collection_uuid: string,
  track_uuid: string
): Promise<boolean> {
  const [result] = await db()
    .select()
    .from(collectionTracks)
    .where(
      and(
        eq(collectionTracks.collection_uuid, collection_uuid),
        eq(collectionTracks.track_uuid, track_uuid)
      )
    )
    .limit(1);

  return !!result;
}

// Count tracks in collection
export async function countCollectionTracks(
  collection_uuid: string
): Promise<number> {
  const result = await db()
    .select({ count: sql`count(*)` })
    .from(collectionTracks)
    .where(eq(collectionTracks.collection_uuid, collection_uuid));

  return Number(result[0]?.count || 0);
}

// Get collection with track count
export async function getCollectionWithTrackCount(
  uuid: string
): Promise<(typeof userTrackCollections.$inferSelect & { track_count: number }) | undefined> {
  const [result] = await db()
    .select({
      id: userTrackCollections.id,
      uuid: userTrackCollections.uuid,
      user_uuid: userTrackCollections.user_uuid,
      name: userTrackCollections.name,
      description: userTrackCollections.description,
      is_public: userTrackCollections.is_public,
      created_at: userTrackCollections.created_at,
      updated_at: userTrackCollections.updated_at,
      track_count: sql<number>`count(${collectionTracks.track_uuid})`,
    })
    .from(userTrackCollections)
    .leftJoin(
      collectionTracks,
      eq(userTrackCollections.uuid, collectionTracks.collection_uuid)
    )
    .where(eq(userTrackCollections.uuid, uuid))
    .groupBy(userTrackCollections.id)
    .limit(1);

  return result;
}

// Get user collections with track counts
export async function getUserCollectionsWithTrackCounts(
  user_uuid: string
): Promise<(typeof userTrackCollections.$inferSelect & { track_count: number })[]> {
  const result = await db()
    .select({
      id: userTrackCollections.id,
      uuid: userTrackCollections.uuid,
      user_uuid: userTrackCollections.user_uuid,
      name: userTrackCollections.name,
      description: userTrackCollections.description,
      is_public: userTrackCollections.is_public,
      created_at: userTrackCollections.created_at,
      updated_at: userTrackCollections.updated_at,
      track_count: sql<number>`count(${collectionTracks.track_uuid})`,
    })
    .from(userTrackCollections)
    .leftJoin(
      collectionTracks,
      eq(userTrackCollections.uuid, collectionTracks.collection_uuid)
    )
    .where(eq(userTrackCollections.user_uuid, user_uuid))
    .groupBy(userTrackCollections.id)
    .orderBy(desc(userTrackCollections.created_at));

  return result;
}

// Get collection statistics
export async function getCollectionStats(user_uuid?: string): Promise<{
  total_collections: number;
  public_collections: number;
  private_collections: number;
  total_collection_tracks: number;
  average_tracks_per_collection: number;
}> {
  const collections = user_uuid
    ? await db().select().from(userTrackCollections).where(eq(userTrackCollections.user_uuid, user_uuid))
    : await db().select().from(userTrackCollections);

  const stats = {
    total_collections: collections.length,
    public_collections: 0,
    private_collections: 0,
    total_collection_tracks: 0,
    average_tracks_per_collection: 0,
  };

  collections.forEach((collection) => {
    if (collection.is_public) {
      stats.public_collections++;
    } else {
      stats.private_collections++;
    }
  });

  // Count total collection tracks
  const trackCountResult = user_uuid
    ? await db()
        .select({ count: sql`count(*)` })
        .from(collectionTracks)
        .innerJoin(
          userTrackCollections,
          eq(collectionTracks.collection_uuid, userTrackCollections.uuid)
        )
        .where(eq(userTrackCollections.user_uuid, user_uuid))
    : await db()
        .select({ count: sql`count(*)` })
        .from(collectionTracks);
  stats.total_collection_tracks = Number(trackCountResult[0]?.count || 0);

  if (stats.total_collections > 0) {
    stats.average_tracks_per_collection = stats.total_collection_tracks / stats.total_collections;
  }

  return stats;
}
